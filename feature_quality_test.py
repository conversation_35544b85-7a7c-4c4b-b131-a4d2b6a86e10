#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 特征质量测试脚本
测试并识别低质量特征，验证哪些特征导致AUC下降
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_feature_quality():
    """测试特征质量"""
    try:
        # 导入P.py
        sys.path.append('.')
        import P
        
        logging.info("🚀 开始特征质量测试...")
        
        # 创建测试数据
        test_data = create_test_data()
        logging.info(f"📊 测试数据准备完成: {test_data.shape}")
        
        # 测试不同特征集的AUC
        results = {}
        
        # 1. 测试21个EFFECTIVE_FEATURES
        logging.info("\n🧪 测试1: 21个EFFECTIVE_FEATURES")
        effective_features = P.EFFECTIVE_FEATURES.copy()
        auc1 = quick_auc_test(test_data, effective_features)
        results['EFFECTIVE_FEATURES'] = {'auc': auc1, 'count': len(effective_features)}
        logging.info(f"   AUC: {auc1:.4f}, 特征数: {len(effective_features)}")
        
        # 2. 测试完整特征工程后的特征
        logging.info("\n🧪 测试2: 完整特征工程")
        try:
            enhanced_data = test_data.copy()
            enhanced_data = P.create_feature_interactions(enhanced_data)
            enhanced_data = P.create_limit_up_feature_interactions(enhanced_data)
            
            # 获取所有数值特征
            exclude_columns = ['ts_code', 'trade_date', 'name', 'industry', 'area', 'market', 'list_date']
            all_features = [col for col in enhanced_data.columns 
                           if col not in exclude_columns and 
                           enhanced_data[col].dtype in ['float64', 'int64', 'float32', 'int32']]
            
            auc2 = quick_auc_test(enhanced_data, all_features)
            results['FULL_FEATURES'] = {'auc': auc2, 'count': len(all_features)}
            logging.info(f"   AUC: {auc2:.4f}, 特征数: {len(all_features)}")
            
        except Exception as e:
            logging.error(f"   完整特征工程测试失败: {e}")
            results['FULL_FEATURES'] = {'auc': 0.5, 'count': 0}
        
        # 3. 测试缺失特征的影响
        logging.info("\n🧪 测试3: 缺失特征影响")
        missing_features = [
            'concept_count', 'industry_count', 'total_sectors', 'avg_member_count',
            'avg_sector_pct_change', 'max_sector_pct_change', 'min_sector_pct_change',
            'sector_volatility', 'strong_sectors_count', 'avg_return_rank',
            'avg_sharpe_rank', 'best_sector_return', 'sector_momentum',
            'sector_diversity', 'is_chip_concentrating'
        ]
        
        # 添加这些缺失特征（用随机值模拟）
        test_data_with_missing = test_data.copy()
        for feature in missing_features:
            test_data_with_missing[feature] = np.random.normal(0, 1, len(test_data_with_missing))
        
        combined_features = effective_features + missing_features
        auc3 = quick_auc_test(test_data_with_missing, combined_features)
        results['WITH_MISSING'] = {'auc': auc3, 'count': len(combined_features)}
        logging.info(f"   AUC: {auc3:.4f}, 特征数: {len(combined_features)}")
        
        # 4. 分析结果
        logging.info("\n📊 测试结果分析:")
        for name, result in results.items():
            status = "✅" if result['auc'] > 0.6 else "⚠️" if result['auc'] > 0.55 else "❌"
            logging.info(f"   {status} {name:20s}: AUC={result['auc']:.4f}, 特征数={result['count']}")
        
        # 5. 给出建议
        logging.info("\n💡 建议:")
        best_auc = max(results.values(), key=lambda x: x['auc'])['auc']
        best_method = [k for k, v in results.items() if v['auc'] == best_auc][0]
        
        if best_method == 'EFFECTIVE_FEATURES':
            logging.info("   ✅ 建议使用21个EFFECTIVE_FEATURES，避免特征污染")
        elif best_auc < 0.55:
            logging.info("   ❌ 所有特征集效果都不佳，需要检查数据质量")
        else:
            logging.info(f"   ⚠️ 最佳方法: {best_method} (AUC={best_auc:.4f})")
        
        return results
        
    except Exception as e:
        logging.error(f"❌ 特征质量测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def create_test_data():
    """创建测试数据"""
    np.random.seed(42)
    n_samples = 10000
    
    # 基础特征
    data = {
        'ts_code': [f'{i:06d}.SZ' for i in range(n_samples)],
        'trade_date': ['20250730'] * n_samples,
        'close': np.random.uniform(5, 50, n_samples),
        'open': np.random.uniform(5, 50, n_samples),
        'high': np.random.uniform(5, 55, n_samples),
        'low': np.random.uniform(3, 50, n_samples),
        'vol': np.random.uniform(100000, 10000000, n_samples),
        'amount': np.random.uniform(1000000, 100000000, n_samples),
        'pct_chg': np.random.uniform(-10, 10, n_samples),
        'change': np.random.uniform(-5, 5, n_samples),
        'pre_close': np.random.uniform(5, 50, n_samples),
        'turnover_rate': np.random.uniform(0.1, 10, n_samples),
        'turnover_rate_f': np.random.uniform(0.1, 10, n_samples),
        'volume_ratio': np.random.uniform(0.5, 3, n_samples),
        'pe': np.random.uniform(5, 100, n_samples),
        'pe_ttm': np.random.uniform(5, 100, n_samples),
        'pb': np.random.uniform(0.5, 10, n_samples),
        'ps': np.random.uniform(1, 20, n_samples),
        'ps_ttm': np.random.uniform(1, 20, n_samples),
        'dv_ratio': np.random.uniform(0, 0.1, n_samples),
        'dv_ttm': np.random.uniform(0, 0.1, n_samples),
        'total_share': np.random.uniform(100000, 10000000, n_samples),
        'float_share': np.random.uniform(50000, 8000000, n_samples),
    }
    
    df = pd.DataFrame(data)
    
    # 创建目标变量（模拟涨停预测）
    # 基于一些特征创建有意义的目标变量
    df['future_1_day_limit_up'] = (
        (df['pct_chg'] > 5) & 
        (df['volume_ratio'] > 1.5) & 
        (df['turnover_rate'] > 2)
    ).astype(int)
    
    return df

def quick_auc_test(df, features, target_col='future_1_day_limit_up'):
    """快速AUC测试"""
    try:
        from sklearn.ensemble import RandomForestClassifier
        from sklearn.metrics import roc_auc_score
        from sklearn.model_selection import train_test_split
        
        # 检查特征可用性
        available_features = [f for f in features if f in df.columns]
        if len(available_features) == 0:
            return 0.5
        
        # 检查目标变量
        if target_col not in df.columns:
            return 0.5
        
        # 准备数据
        X = df[available_features].fillna(df[available_features].median())
        y = df[target_col]
        
        # 移除缺失值
        mask = ~(X.isna().any(axis=1) | y.isna())
        X = X[mask]
        y = y[mask]
        
        if len(y.unique()) < 2 or len(X) < 100:
            return 0.5
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        # 训练模型
        model = RandomForestClassifier(
            n_estimators=50,
            random_state=42,
            max_depth=5
        )
        
        model.fit(X_train, y_train)
        
        # 预测和评估
        y_pred = model.predict_proba(X_test)[:, 1]
        auc = roc_auc_score(y_test, y_pred)
        
        return auc
        
    except Exception as e:
        logging.warning(f"AUC测试失败: {e}")
        return 0.5

if __name__ == "__main__":
    test_feature_quality()
