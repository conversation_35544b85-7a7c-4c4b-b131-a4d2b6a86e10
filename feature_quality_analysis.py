#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 特征质量分析脚本
分析特征重复、噪声、缺失值填充等问题
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def analyze_feature_quality():
    """分析特征质量问题"""
    try:
        # 导入P.py
        sys.path.append('.')
        import P
        
        logging.info("🚀 开始特征质量分析...")
        
        # 创建测试数据
        test_data = create_realistic_test_data()
        logging.info(f"📊 测试数据准备完成: {test_data.shape}")
        
        # 1. 分析特征生成过程
        logging.info("\n🔍 分析特征生成过程...")
        
        original_cols = set(test_data.columns)
        logging.info(f"原始特征数: {len(original_cols)}")
        
        # 调用特征工程
        enhanced_data = test_data.copy()
        if hasattr(P, 'create_feature_interactions'):
            enhanced_data = P.create_feature_interactions(enhanced_data)
            
        if hasattr(P, 'create_limit_up_feature_interactions'):
            enhanced_data = P.create_limit_up_feature_interactions(enhanced_data)
        
        new_cols = set(enhanced_data.columns)
        added_features = new_cols - original_cols
        
        logging.info(f"增强后特征数: {len(new_cols)}")
        logging.info(f"新增特征数: {len(added_features)}")
        logging.info(f"新增特征示例: {list(added_features)[:10]}")
        
        # 2. 分析特征重复
        logging.info("\n🔍 分析特征重复...")
        duplicate_analysis = analyze_feature_duplicates(enhanced_data)
        
        # 3. 分析特征质量
        logging.info("\n🔍 分析特征质量...")
        quality_analysis = analyze_feature_noise(enhanced_data)
        
        # 4. 分析缺失值填充
        logging.info("\n🔍 分析缺失值填充...")
        missing_analysis = analyze_missing_value_filling(enhanced_data)
        
        # 5. 综合分析
        logging.info("\n📊 综合分析结果:")
        
        # 重复特征问题
        if duplicate_analysis['high_correlation_pairs'] > 0:
            logging.warning(f"   ❌ 发现{duplicate_analysis['high_correlation_pairs']}对高度相关特征")
            logging.warning(f"   ❌ 可能存在特征重复问题")
        else:
            logging.info(f"   ✅ 未发现明显的特征重复")
        
        # 噪声特征问题
        if quality_analysis['low_variance_features'] > 0:
            logging.warning(f"   ❌ 发现{quality_analysis['low_variance_features']}个低方差特征")
            logging.warning(f"   ❌ 可能存在噪声特征")
        else:
            logging.info(f"   ✅ 特征方差分布正常")
        
        # 缺失值填充问题
        if missing_analysis['suspicious_patterns'] > 0:
            logging.warning(f"   ❌ 发现{missing_analysis['suspicious_patterns']}个可疑的填充模式")
            logging.warning(f"   ❌ 缺失值填充可能引入噪声")
        else:
            logging.info(f"   ✅ 缺失值填充模式正常")
        
        # 6. 给出建议
        logging.info("\n💡 建议:")
        
        total_issues = (duplicate_analysis['high_correlation_pairs'] + 
                       quality_analysis['low_variance_features'] + 
                       missing_analysis['suspicious_patterns'])
        
        if total_issues == 0:
            logging.info("   ✅ 特征质量良好，无明显问题")
        else:
            logging.info(f"   ❌ 发现{total_issues}个潜在问题，建议:")
            logging.info("     1. 移除高度相关的重复特征")
            logging.info("     2. 过滤低方差的噪声特征")
            logging.info("     3. 改进缺失值填充策略")
            logging.info("     4. 使用特征选择算法筛选高质量特征")
        
        return {
            'duplicate_analysis': duplicate_analysis,
            'quality_analysis': quality_analysis,
            'missing_analysis': missing_analysis,
            'total_issues': total_issues
        }
        
    except Exception as e:
        logging.error(f"❌ 特征质量分析失败: {e}")
        import traceback
        traceback.print_exc()
        return {}

def analyze_feature_duplicates(df):
    """分析特征重复"""
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    
    if len(numeric_cols) < 2:
        return {'high_correlation_pairs': 0, 'correlation_matrix': None}
    
    # 计算相关性矩阵
    corr_matrix = df[numeric_cols].corr().abs()
    
    # 找出高度相关的特征对（相关性>0.95）
    high_corr_pairs = 0
    for i in range(len(corr_matrix.columns)):
        for j in range(i+1, len(corr_matrix.columns)):
            if corr_matrix.iloc[i, j] > 0.95:
                high_corr_pairs += 1
                if high_corr_pairs <= 5:  # 只显示前5个
                    logging.warning(f"     高相关特征对: {corr_matrix.columns[i]} vs {corr_matrix.columns[j]} (相关性: {corr_matrix.iloc[i, j]:.3f})")
    
    return {
        'high_correlation_pairs': high_corr_pairs,
        'correlation_matrix': corr_matrix
    }

def analyze_feature_noise(df):
    """分析特征噪声"""
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    
    if len(numeric_cols) == 0:
        return {'low_variance_features': 0, 'constant_features': 0}
    
    # 计算方差
    variances = df[numeric_cols].var()
    
    # 找出低方差特征（方差接近0）
    low_variance_features = (variances < 1e-6).sum()
    constant_features = (variances == 0).sum()
    
    if low_variance_features > 0:
        low_var_cols = variances[variances < 1e-6].index[:5]  # 显示前5个
        logging.warning(f"     低方差特征示例: {list(low_var_cols)}")
    
    return {
        'low_variance_features': low_variance_features,
        'constant_features': constant_features,
        'variance_stats': variances.describe()
    }

def analyze_missing_value_filling(df):
    """分析缺失值填充模式"""
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    
    suspicious_patterns = 0
    
    for col in numeric_cols:
        # 检查是否有大量相同值（可能是填充值）
        value_counts = df[col].value_counts()
        if len(value_counts) > 0:
            most_common_ratio = value_counts.iloc[0] / len(df)
            
            # 如果某个值占比超过80%，可能是填充值
            if most_common_ratio > 0.8:
                suspicious_patterns += 1
                if suspicious_patterns <= 5:  # 只显示前5个
                    logging.warning(f"     可疑填充模式: {col} 中值 {value_counts.index[0]} 占比 {most_common_ratio:.1%}")
    
    return {
        'suspicious_patterns': suspicious_patterns
    }

def create_realistic_test_data():
    """创建更真实的测试数据"""
    np.random.seed(42)
    n_samples = 1000
    
    # 基础特征
    data = {
        'ts_code': [f'{i:06d}.SZ' for i in range(n_samples)],
        'trade_date': ['20250730'] * n_samples,
        'close': np.random.uniform(5, 50, n_samples),
        'open': np.random.uniform(5, 50, n_samples),
        'high': np.random.uniform(5, 55, n_samples),
        'low': np.random.uniform(3, 50, n_samples),
        'vol': np.random.uniform(100000, 10000000, n_samples),
        'amount': np.random.uniform(1000000, 100000000, n_samples),
        'pct_chg': np.random.uniform(-10, 10, n_samples),
        'change': np.random.uniform(-5, 5, n_samples),
        'pre_close': np.random.uniform(5, 50, n_samples),
        'turnover_rate': np.random.uniform(0.1, 10, n_samples),
        'turnover_rate_f': np.random.uniform(0.1, 10, n_samples),
        'volume_ratio': np.random.uniform(0.5, 3, n_samples),
        'pe': np.random.uniform(5, 100, n_samples),
        'pe_ttm': np.random.uniform(5, 100, n_samples),
        'pb': np.random.uniform(0.5, 10, n_samples),
        'ps': np.random.uniform(1, 20, n_samples),
        'ps_ttm': np.random.uniform(1, 20, n_samples),
        'dv_ratio': np.random.uniform(0, 0.1, n_samples),
        'dv_ttm': np.random.uniform(0, 0.1, n_samples),
        'total_share': np.random.uniform(100000, 10000000, n_samples),
        'float_share': np.random.uniform(50000, 8000000, n_samples),
    }
    
    # 添加一些模拟的技术指标
    data.update({
        'rsi6': np.random.uniform(0, 100, n_samples),
        'macd_hist': np.random.uniform(-1, 1, n_samples),
        'kdj_k': np.random.uniform(0, 100, n_samples),
        'net_mf_amount': np.random.uniform(-1000000, 1000000, n_samples),
    })
    
    # 模拟一些缺失值
    df = pd.DataFrame(data)
    
    # 随机设置一些缺失值
    for col in ['rsi6', 'macd_hist', 'net_mf_amount']:
        missing_mask = np.random.random(n_samples) < 0.1  # 10%缺失率
        df.loc[missing_mask, col] = np.nan
    
    # 创建目标变量
    df['future_1_day_limit_up'] = (
        (df['pct_chg'] > 5) & 
        (df['volume_ratio'] > 1.5) & 
        (df['turnover_rate'] > 2)
    ).astype(int)
    
    return df

if __name__ == "__main__":
    analyze_feature_quality()
