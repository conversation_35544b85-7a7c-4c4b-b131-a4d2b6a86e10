#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 特征一致性测试脚本
验证超参数调优、模型训练、预测三个阶段使用的特征是否一致
"""

import sys
import os
import pandas as pd
import numpy as np
import logging
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_feature_consistency():
    """测试三个阶段的特征一致性"""
    try:
        # 导入P.py
        sys.path.append('.')
        import P
        
        logging.info("🚀 开始特征一致性测试...")
        
        # 创建测试数据
        test_data = create_comprehensive_test_data()
        logging.info(f"📊 测试数据准备完成: {test_data.shape}")
        
        # 存储三个阶段的特征信息
        results = {}
        
        # 1. 测试预测阶段特征
        logging.info("\n🧪 测试1: 预测阶段特征")
        try:
            prediction_features = P.get_all_feature_columns(test_data.copy())
            results['prediction'] = {
                'features': prediction_features,
                'count': len(prediction_features),
                'source': 'get_all_feature_columns'
            }
            logging.info(f"   特征数量: {len(prediction_features)}")
            logging.info(f"   前10个特征: {prediction_features[:10]}")
        except Exception as e:
            logging.error(f"   预测阶段测试失败: {e}")
            results['prediction'] = {'features': [], 'count': 0, 'source': 'failed'}
        
        # 2. 测试训练阶段特征（模拟训练数据预处理）
        logging.info("\n🧪 测试2: 训练阶段特征")
        try:
            # 模拟训练阶段的数据预处理流程
            training_data = test_data.copy()
            
            # 调用训练阶段可能使用的特征工程函数
            if hasattr(P, 'create_feature_interactions'):
                training_data = P.create_feature_interactions(training_data)
                logging.info("   ✅ 调用了create_feature_interactions")
            
            if hasattr(P, 'create_limit_up_feature_interactions'):
                training_data = P.create_limit_up_feature_interactions(training_data)
                logging.info("   ✅ 调用了create_limit_up_feature_interactions")
            
            # 获取所有数值特征
            exclude_columns = ['ts_code', 'trade_date', 'name', 'industry', 'area', 'market', 'list_date']
            training_features = [col for col in training_data.columns 
                               if col not in exclude_columns and 
                               training_data[col].dtype in ['float64', 'int64', 'float32', 'int32']]
            
            results['training'] = {
                'features': training_features,
                'count': len(training_features),
                'source': 'full_feature_engineering'
            }
            logging.info(f"   特征数量: {len(training_features)}")
            logging.info(f"   前10个特征: {training_features[:10]}")
            
        except Exception as e:
            logging.error(f"   训练阶段测试失败: {e}")
            results['training'] = {'features': [], 'count': 0, 'source': 'failed'}
        
        # 3. 测试超参数调优阶段特征（检查是否使用相同流程）
        logging.info("\n🧪 测试3: 超参数调优阶段特征")
        try:
            # 超参数调优通常使用与训练相同的特征工程
            # 这里我们假设它使用相同的流程
            optimization_features = results['training']['features'].copy()
            results['optimization'] = {
                'features': optimization_features,
                'count': len(optimization_features),
                'source': 'same_as_training'
            }
            logging.info(f"   特征数量: {len(optimization_features)}")
            logging.info(f"   前10个特征: {optimization_features[:10]}")
            
        except Exception as e:
            logging.error(f"   超参数调优阶段测试失败: {e}")
            results['optimization'] = {'features': [], 'count': 0, 'source': 'failed'}
        
        # 4. 对比分析
        logging.info("\n📊 特征一致性分析:")
        
        # 检查特征数量一致性
        counts = {stage: info['count'] for stage, info in results.items()}
        logging.info(f"   特征数量对比:")
        for stage, count in counts.items():
            logging.info(f"     {stage:12s}: {count:4d} 个特征")
        
        # 检查是否一致
        unique_counts = set(counts.values())
        if len(unique_counts) == 1:
            logging.info("   ✅ 特征数量一致")
            consistency_status = "CONSISTENT"
        else:
            logging.error("   ❌ 特征数量不一致！")
            consistency_status = "INCONSISTENT"
        
        # 检查特征名称一致性
        if len(results) >= 2:
            stages = list(results.keys())
            base_features = set(results[stages[0]]['features'])
            
            for stage in stages[1:]:
                stage_features = set(results[stage]['features'])
                
                # 计算交集和差集
                common = base_features & stage_features
                only_in_base = base_features - stage_features
                only_in_stage = stage_features - base_features
                
                logging.info(f"\n   {stages[0]} vs {stage}:")
                logging.info(f"     共同特征: {len(common)} 个")
                if only_in_base:
                    logging.info(f"     仅在{stages[0]}中: {len(only_in_base)} 个")
                    logging.info(f"       示例: {list(only_in_base)[:5]}")
                if only_in_stage:
                    logging.info(f"     仅在{stage}中: {len(only_in_stage)} 个")
                    logging.info(f"       示例: {list(only_in_stage)[:5]}")
        
        # 5. 给出建议
        logging.info(f"\n💡 建议:")
        if consistency_status == "CONSISTENT":
            logging.info("   ✅ 特征一致性良好，无需修复")
        else:
            logging.info("   ❌ 发现特征不一致问题，需要修复:")
            logging.info("     1. 确保所有阶段使用相同的特征工程函数")
            logging.info("     2. 统一特征选择策略")
            logging.info("     3. 避免在不同阶段使用不同的特征过滤逻辑")
            
            # 具体修复建议
            max_count = max(counts.values())
            min_count = min(counts.values())
            
            if max_count > 100 and min_count < 50:
                logging.info("     🔧 建议：统一使用21个EFFECTIVE_FEATURES")
                logging.info("     🔧 修复方法：让所有阶段都调用相同的get_all_feature_columns函数")
        
        return results, consistency_status
        
    except Exception as e:
        logging.error(f"❌ 特征一致性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return {}, "FAILED"

def create_comprehensive_test_data():
    """创建全面的测试数据"""
    np.random.seed(42)
    n_samples = 1000
    
    # 基础特征（EFFECTIVE_FEATURES）
    data = {
        'ts_code': [f'{i:06d}.SZ' for i in range(n_samples)],
        'trade_date': ['20250730'] * n_samples,
        'close': np.random.uniform(5, 50, n_samples),
        'open': np.random.uniform(5, 50, n_samples),
        'high': np.random.uniform(5, 55, n_samples),
        'low': np.random.uniform(3, 50, n_samples),
        'vol': np.random.uniform(100000, 10000000, n_samples),
        'amount': np.random.uniform(1000000, 100000000, n_samples),
        'pct_chg': np.random.uniform(-10, 10, n_samples),
        'change': np.random.uniform(-5, 5, n_samples),
        'pre_close': np.random.uniform(5, 50, n_samples),
        'turnover_rate': np.random.uniform(0.1, 10, n_samples),
        'turnover_rate_f': np.random.uniform(0.1, 10, n_samples),
        'volume_ratio': np.random.uniform(0.5, 3, n_samples),
        'pe': np.random.uniform(5, 100, n_samples),
        'pe_ttm': np.random.uniform(5, 100, n_samples),
        'pb': np.random.uniform(0.5, 10, n_samples),
        'ps': np.random.uniform(1, 20, n_samples),
        'ps_ttm': np.random.uniform(1, 20, n_samples),
        'dv_ratio': np.random.uniform(0, 0.1, n_samples),
        'dv_ttm': np.random.uniform(0, 0.1, n_samples),
        'total_share': np.random.uniform(100000, 10000000, n_samples),
        'float_share': np.random.uniform(50000, 8000000, n_samples),
    }
    
    # 添加一些可能用于特征工程的额外字段
    data.update({
        'name': [f'股票{i}' for i in range(n_samples)],
        'industry': ['制造业'] * n_samples,
        'area': ['广东'] * n_samples,
        'market': ['主板'] * n_samples,
        'list_date': ['20200101'] * n_samples,
    })
    
    df = pd.DataFrame(data)
    
    # 创建目标变量
    df['future_1_day_limit_up'] = (
        (df['pct_chg'] > 5) & 
        (df['volume_ratio'] > 1.5) & 
        (df['turnover_rate'] > 2)
    ).astype(int)
    
    return df

if __name__ == "__main__":
    results, status = test_feature_consistency()
    print(f"\n🎯 最终结果: {status}")
